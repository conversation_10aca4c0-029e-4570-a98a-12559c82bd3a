import React, { useState, useEffect } from 'react';
import firebaseService from './services/firebaseService';

const Reports = () => {
  const [sales, setSales] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSales();
  }, []);

  const loadSales = async () => {
    try {
      setLoading(true);
      const data = await firebaseService.getSales();
      setSales(data || []);
    } catch (error) {
      console.error('Error loading sales:', error);
      const savedSales = JSON.parse(localStorage.getItem('sales') || '[]');
      setSales(savedSales || []);
    } finally {
      setLoading(false);
    }
  };

  // حساب الإحصائيات بطريقة آمنة
  let totalSales = 0;
  let todaysTotal = 0;
  let todaysSalesCount = 0;
  let averageSale = 0;

  try {
    if (sales && sales.length > 0) {
      // حساب إجمالي المبيعات
      totalSales = sales.reduce((sum, sale) => {
        return sum + (Number(sale.total) || 0);
      }, 0);

      // حساب مبيعات اليوم
      const today = new Date().toLocaleDateString('ar-EG');
      const todaysSales = sales.filter(sale => sale.date === today);
      todaysSalesCount = todaysSales.length;
      todaysTotal = todaysSales.reduce((sum, sale) => {
        return sum + (Number(sale.total) || 0);
      }, 0);

      // حساب متوسط البيع
      averageSale = totalSales / sales.length;
    }
  } catch (error) {
    console.error('Error calculating stats:', error);
  }

  // دالة حذف البيع مع إعادة الكميات
  const deleteSale = async (id) => {
    const saleToDelete = sales.find(s => s.id === id);
    if (!saleToDelete) {
      alert('❌ البيع غير موجود');
      return;
    }

    const itemsDetails = (saleToDelete.items || []).map(item =>
      `• ${item.name}: ${item.quantity} قطعة`
    ).join('\n');

    const confirmMessage = `❗ هل تريد حذف هذا البيع؟\n\n` +
      `💰 المبلغ: ${(saleToDelete.total || 0).toFixed(2)} د.ع\n` +
      `📦 المنتجات:\n${itemsDetails}\n\n` +
      `🔄 سيتم إعادة جميع الكميات للمخزون\n\n` +
      `⚠️ هذا الإجراء لا يمكن التراجع عنه`;

    if (window.confirm(confirmMessage)) {
      try {
        setLoading(true);
        await firebaseService.deleteSale(id);
        await loadSales(); // إعادة تحميل البيانات
        alert('✅ تم حذف البيع بنجاح وإعادة الكميات للمخزون');
      } catch (error) {
        console.error('Error deleting sale:', error);

        // Fallback: حذف محلي مع إعادة الكميات
        try {
          // إعادة الكميات محلياً
          if (saleToDelete.items) {
            const savedProducts = JSON.parse(localStorage.getItem('products') || '[]');
            const updatedProducts = savedProducts.map(product => {
              const saleItem = saleToDelete.items.find(item => item.productId === product.id);
              if (saleItem) {
                return {
                  ...product,
                  quantity: product.quantity + saleItem.quantity
                };
              }
              return product;
            });
            localStorage.setItem('products', JSON.stringify(updatedProducts));
          }

          // حذف البيع محلياً
          const updatedSales = sales.filter((s) => s.id !== id);
          setSales(updatedSales);
          localStorage.setItem('sales', JSON.stringify(updatedSales));
          alert('✅ تم حذف البيع محلياً وإعادة الكميات للمخزون');
        } catch (fallbackError) {
          alert('❌ حدث خطأ في حذف البيع: ' + error.message);
        }
      } finally {
        setLoading(false);
      }
    }
  };

  // دالة تصدير البيانات
  const exportData = async () => {
    try {
      const products = await firebaseService.getProducts();
      const backupData = {
        exportDate: new Date().toISOString(),
        exportTime: new Date().toLocaleString('ar-EG'),
        products: products,
        sales: sales,
        summary: {
          totalProducts: products.length,
          totalSales: sales.length,
          totalRevenue: sales.reduce((sum, sale) => sum + (sale.total || 0), 0)
        }
      };

      const dataStr = JSON.stringify(backupData, null, 2);
      const dataBlob = new Blob([dataStr], {type: 'application/json'});
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `pos-backup-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      alert('✅ تم تصدير النسخة الاحتياطية بنجاح!');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('❌ حدث خطأ في تصدير البيانات');
    }
  };

  // عرض شاشة التحميل
  if (loading) {
    return (
      <div className="min-h-screen p-8 flex items-center justify-center">
        <div className="glass p-8 rounded-3xl text-center">
          <div className="text-6xl mb-4">⏳</div>
          <p className="text-xl text-white">جاري تحميل التقارير...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">📊 التقارير والمبيعات</h1>
        <p className="text-white text-opacity-80">تحليل الأداء والمبيعات</p>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        <div className="glass p-8 rounded-3xl text-center shadow-glow animate-fadeInUp card-hover">
          <div className="text-5xl mb-4">📈</div>
          <h3 className="text-xl font-bold text-white mb-3">إجمالي المبيعات</h3>
          <p className="text-4xl font-bold text-green-300 mb-2">{totalSales.toFixed(2)} د.ع</p>
          <p className="text-sm text-white text-opacity-70">{sales.length} عملية بيع</p>
        </div>

        <div className="glass p-8 rounded-3xl text-center shadow-glow-green animate-fadeInUp card-hover" style={{ animationDelay: '0.1s' }}>
          <div className="text-5xl mb-4">📅</div>
          <h3 className="text-xl font-bold text-white mb-3">مبيعات اليوم</h3>
          <p className="text-4xl font-bold text-green-300 mb-2">{todaysTotal.toFixed(2)} د.ع</p>
          <p className="text-sm text-white text-opacity-70">{todaysSalesCount} عملية بيع</p>
        </div>

        <div className="glass p-8 rounded-3xl text-center shadow-glow animate-fadeInUp card-hover" style={{ animationDelay: '0.2s' }}>
          <div className="text-5xl mb-4">💰</div>
          <h3 className="text-xl font-bold text-white mb-3">متوسط البيع</h3>
          <p className="text-4xl font-bold text-purple-300 mb-2">
            {averageSale.toFixed(2)} د.ع
          </p>
          <p className="text-sm text-white text-opacity-70">لكل عملية بيع</p>
        </div>
      </div>

      {/* قائمة المبيعات */}
      <div className="glass rounded-3xl shadow-glow animate-fadeInUp">
        <div className="p-6 border-b border-white border-opacity-20">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <span className="text-3xl ml-3">🧾</span>
              سجل المبيعات ({sales.length})
            </h2>
            <div className="flex gap-3">
              <button
                onClick={exportData}
                className="bg-green-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-300 transform hover:scale-105"
              >
                <span className="flex items-center">
                  <span className="ml-1">💾</span>
                  نسخة احتياطية
                </span>
              </button>
              <button
                onClick={loadSales}
                disabled={loading}
                className="bg-blue-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-300 transform hover:scale-105 disabled:opacity-50"
              >
                <span className="flex items-center">
                  <span className="ml-1">🔄</span>
                  {loading ? 'جاري التحديث...' : 'تحديث'}
                </span>
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {sales.length === 0 ? (
            <div className="text-center py-16 text-white text-opacity-60">
              <div className="text-6xl mb-4">📭</div>
              <p className="text-xl mb-2">لا توجد مبيعات بعد</p>
              <p className="text-sm opacity-80">ابدأ بإضافة مبيعات من صفحة نقطة البيع</p>
            </div>
          ) : (
            <div className="space-y-6">
              {sales.slice(0, 10).map((sale, index) => (
                <div key={sale.id} className="bg-white bg-opacity-10 backdrop-blur-sm rounded-3xl p-6 hover:bg-opacity-20 transition-all duration-300 card-hover animate-slideInRight" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="flex justify-between items-start mb-4">
                    <div className="text-white">
                      <h3 className="font-bold text-xl mb-2 flex items-center">
                        <span className="text-2xl ml-2">🧾</span>
                        فاتورة #{sale.id}
                      </h3>
                      <div className="space-y-1 text-sm text-white text-opacity-80">
                        <p className="flex items-center">
                          <span className="ml-2">📅</span>
                          {sale.date} - ⏰ {sale.time}
                        </p>
                        {sale.customer && (
                          <p className="flex items-center">
                            <span className="ml-2">👤</span>
                            العميل: {sale.customer}
                          </p>
                        )}
                        <p className="flex items-center">
                          <span className="ml-2">💳</span>
                          الدفع: {sale.paymentMethod}
                        </p>
                        {sale.discount > 0 && (
                          <p className="flex items-center text-yellow-300">
                            <span className="ml-2">🏷️</span>
                            خصم: {sale.discount.toFixed(2)} د.ع
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-green-300 mb-3">{(sale.total || 0).toFixed(2)} د.ع</p>
                      <p className="text-sm text-white text-opacity-60 mb-3">
                        {(sale.items || []).length} منتج
                      </p>
                      <button
                        onClick={() => deleteSale(sale.id)}
                        className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-300 transform hover:scale-105"
                        title={`حذف البيع وإعادة ${(sale.items || []).reduce((sum, item) => sum + item.quantity, 0)} قطعة للمخزون`}
                      >
                        <span className="flex items-center">
                          <span className="ml-1">🗑️</span>
                          حذف + إعادة للمخزون
                        </span>
                      </button>
                    </div>
                  </div>

                  <div className="border-t border-white border-opacity-20 pt-4">
                    <p className="text-white font-medium mb-3 flex items-center">
                      <span className="text-lg ml-2">📦</span>
                      المنتجات:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {sale.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="bg-white bg-opacity-10 rounded-2xl p-3 text-sm text-white backdrop-blur-sm">
                          <div className="font-medium">{item.name}</div>
                          <div className="text-white text-opacity-70">
                            {item.quantity} × {item.price} = {item.total} د.ع
                          </div>
                          {item.category && (
                            <div className="text-xs text-white text-opacity-60 bg-white bg-opacity-10 px-2 py-1 rounded-full inline-block mt-1">
                              {item.category}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Reports;
