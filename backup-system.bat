@echo off
echo ========================================
echo       نظام النسخ الاحتياطي الذكي
echo ========================================

set BACKUP_DIR=C:\POS-Backups
set DATE=%date:~-4,4%-%date:~-10,2%-%date:~-7,2%
set TIME=%time:~0,2%-%time:~3,2%-%time:~6,2%
set BACKUP_NAME=POS-Backup-%DATE%_%TIME%

echo جاري إنشاء نسخة احتياطية...
echo المجلد: %BACKUP_DIR%\%BACKUP_NAME%

:: إنشاء مجلد النسخ الاحتياطية
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
mkdir "%BACKUP_DIR%\%BACKUP_NAME%"

:: نسخ ملفات المشروع
echo نسخ ملفات المشروع...
xcopy "C:\Users\<USER>\Documents\alihatem" "%BACKUP_DIR%\%BACKUP_NAME%\project" /E /I /H /Y

:: إنشاء ملف معلومات
echo تاريخ النسخة: %DATE% %TIME% > "%BACKUP_DIR%\%BACKUP_NAME%\backup-info.txt"
echo مسار المشروع الأصلي: C:\Users\<USER>\Documents\alihatem >> "%BACKUP_DIR%\%BACKUP_NAME%\backup-info.txt"
echo Firebase Project ID: pos-system-bfc74 >> "%BACKUP_DIR%\%BACKUP_NAME%\backup-info.txt"
echo رمز الدخول: alihatem123 >> "%BACKUP_DIR%\%BACKUP_NAME%\backup-info.txt"

:: إنشاء ملف الاستعادة
echo @echo off > "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"
echo echo استعادة نظام نقاط البيع... >> "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"
echo xcopy "project" "C:\Users\<USER>\Documents\alihatem" /E /I /H /Y >> "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"
echo cd "C:\Users\<USER>\Documents\alihatem" >> "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"
echo npm install >> "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"
echo echo تم استعادة النظام بنجاح! >> "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"
echo echo لتشغيل النظام اكتب: npm run dev >> "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"
echo pause >> "%BACKUP_DIR%\%BACKUP_NAME%\restore.bat"

echo ========================================
echo تم إنشاء النسخة الاحتياطية بنجاح!
echo المكان: %BACKUP_DIR%\%BACKUP_NAME%
echo ========================================
echo لاستعادة النظام: شغل ملف restore.bat
echo ========================================
pause
