[debug] [2025-07-14T14:20:44.084Z] ----------------------------------------------------------------------
[debug] [2025-07-14T14:20:44.087Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init hosting
[debug] [2025-07-14T14:20:44.087Z] CLI Version:   14.10.1
[debug] [2025-07-14T14:20:44.087Z] Platform:      win32
[debug] [2025-07-14T14:20:44.088Z] Node Version:  v22.17.0
[debug] [2025-07-14T14:20:44.088Z] Time:          Mon Jul 14 2025 17:20:44 GMT+0300 (Arabian Standard Time)
[debug] [2025-07-14T14:20:44.088Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-14T14:20:44.094Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-14T14:20:44.095Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\Documents\alihatem

